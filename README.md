# AI-Powered Ticket Management System

An intelligent ticket management system that leverages AI to automatically analyze, prioritize, and assign support tickets to the most suitable team members based on their skills and expertise.

## 🚀 Features

### Core Functionality
- **User Authentication & Authorization**: Secure login/signup with role-based access (User, Moderator, Admin)
- **Ticket Management**: Create, view, and manage support tickets with detailed tracking
- **AI-Powered Analysis**: Automatic ticket analysis using Google's Gemini AI model
- **Smart Assignment**: Intelligent ticket assignment based on required skills and team member expertise
- **Email Notifications**: Automated email alerts for ticket assignments and updates
- **Real-time Processing**: Event-driven architecture using Inngest for reliable background processing

### AI Capabilities
- **Automatic Prioritization**: AI analyzes ticket content and assigns priority levels (Low, Medium, High)
- **Skill Extraction**: Identifies required technical skills from ticket descriptions
- **Helpful Notes Generation**: AI provides contextual notes and suggestions for ticket resolution
- **Smart Routing**: Matches tickets with team members based on skill compatibility

### User Roles
- **Users**: Can create and view their own tickets
- **Moderators**: Can be assigned tickets based on their skill set
- **Admins**: Full system access and can handle any ticket type

## 🏗️ Architecture

The system consists of two main components:

### Backend (`ai-ticket-assistant`)
- **Framework**: Express.js with Node.js
- **Database**: MongoDB with Mongoose ODM
- **AI Integration**: Google Gemini AI via Inngest Agent Kit
- **Event Processing**: Inngest for reliable background job processing
- **Authentication**: JWT-based authentication with bcrypt password hashing
- **Email Service**: Nodemailer for automated notifications

### Frontend (`ai-ticket-frontend`)
- **Framework**: React 19 with Vite
- **Styling**: Tailwind CSS with DaisyUI components
- **Routing**: React Router DOM for navigation
- **State Management**: React hooks and context

## 🛠️ Technology Stack

### Backend Dependencies
- **Express.js**: Web application framework
- **MongoDB/Mongoose**: Database and ODM
- **Inngest**: Event-driven background job processing
- **@inngest/agent-kit**: AI agent integration
- **JWT**: Authentication tokens
- **Bcrypt**: Password hashing
- **Nodemailer**: Email service
- **CORS**: Cross-origin resource sharing

### Frontend Dependencies
- **React 19**: UI library
- **Vite**: Build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **DaisyUI**: Component library for Tailwind
- **React Router DOM**: Client-side routing

## 📋 Prerequisites

- Node.js (v18 or higher)
- MongoDB database
- Google Gemini API key
- Email service credentials (for notifications)
- Inngest account (for event processing)

## 🚀 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd AI-powered_ticket_management_system
```

### 2. Backend Setup
```bash
cd ai-ticket-assistant
npm install
```

Create a `.env` file in the `ai-ticket-assistant` directory:
```env
PORT=3000
MONGODB_URI=mongodb://localhost:27017/ticket-system
JWT_SECRET=your-jwt-secret-key
GEMINI_API_KEY=your-gemini-api-key
EMAIL_HOST=your-email-host
EMAIL_PORT=587
EMAIL_USER=your-email-username
EMAIL_PASS=your-email-password
INNGEST_EVENT_KEY=your-inngest-event-key
INNGEST_SIGNING_KEY=your-inngest-signing-key
```

### 3. Frontend Setup
```bash
cd ../ai-ticket-frontend
npm install
```

### 4. Start the Development Servers

#### Backend (Terminal 1)
```bash
cd ai-ticket-assistant
npm run dev
```

#### Inngest Dev Server (Terminal 2)
```bash
cd ai-ticket-assistant
npm run inngest-dev
```

#### Frontend (Terminal 3)
```bash
cd ai-ticket-frontend
npm run dev
```

## 📖 Usage

### For Users
1. **Sign Up**: Create an account with email and password
2. **Create Tickets**: Submit support requests with title and description
3. **Track Progress**: Monitor ticket status and updates
4. **View Details**: Access detailed ticket information and AI-generated notes

### For Moderators
1. **Skill Registration**: Add your technical skills during signup
2. **Receive Assignments**: Get automatically assigned tickets matching your expertise
3. **Email Notifications**: Receive email alerts for new assignments
4. **Manage Tickets**: Update ticket status and add resolution notes

### For Admins
1. **System Overview**: Access all tickets and user management
2. **Fallback Assignment**: Handle tickets when no suitable moderator is found
3. **User Management**: Manage user roles and permissions

## 🔄 Workflow

1. **Ticket Creation**: User submits a new support ticket
2. **AI Analysis**: Gemini AI analyzes the ticket content and extracts:
   - Priority level (Low/Medium/High)
   - Required technical skills
   - Helpful resolution notes
3. **Smart Assignment**: System finds the best moderator based on skill matching
4. **Notification**: Assigned moderator receives email notification
5. **Resolution**: Moderator works on the ticket and updates status
6. **Completion**: Ticket is marked as resolved with final notes

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/signup` - User registration
- `POST /api/auth/login` - User login

### Tickets
- `GET /api/tickets` - Get all tickets (role-based filtering)
- `POST /api/tickets` - Create new ticket
- `GET /api/tickets/:id` - Get specific ticket details
- `PUT /api/tickets/:id` - Update ticket status/details

## 🤖 AI Integration

The system uses Google's Gemini AI model to:
- Analyze ticket descriptions for technical complexity
- Extract relevant skills and technologies mentioned
- Generate priority recommendations
- Provide helpful troubleshooting notes
- Suggest potential solutions or next steps

## 📧 Email Notifications

Automated email notifications are sent for:
- New ticket assignments to moderators
- Ticket status updates
- Priority escalations
- System alerts and updates

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Role-based access control
- CORS protection
- Input validation and sanitization

## 🚀 Deployment

### Production Environment Variables
Ensure all environment variables are properly configured for production:
- Database connection strings
- API keys and secrets
- Email service configuration
- Inngest production keys

### Build Commands
```bash
# Backend
cd ai-ticket-assistant
npm start

# Frontend
cd ai-ticket-frontend
npm run build
npm run preview
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation for troubleshooting guides
