# AI-Powered Ticket Management System

An intelligent ticket management system that leverages AI to automatically analyze, prioritize, and assign support tickets to the most suitable team members.

## Features

- **AI-Powered Analysis**: Automatic ticket analysis using Google Gemini AI
- **Smart Assignment**: Intelligent ticket assignment based on skills
- **Role-Based Access**: User, Moderator, and Admin roles
- **Email Notifications**: Automated alerts for assignments
- **Real-time Processing**: Event-driven architecture with Inngest

## Technology Stack

**Backend**: Express.js, MongoDB, Inngest, Google Gemini AI, JWT, Nodemailer
**Frontend**: React 19, Vite, <PERSON><PERSON>wind CSS, DaisyUI

## Installation & Setup

### Backend Setup
```bash
cd ai-ticket-assistant
npm install
```

Create a `.env` file in the `ai-ticket-assistant` directory:
```env
PORT=3000
MONGODB_URI=mongodb://localhost:27017/ticket-system
JWT_SECRET=your-jwt-secret-key
GEMINI_API_KEY=your-gemini-api-key
EMAIL_HOST=your-email-host
EMAIL_PORT=587
EMAIL_USER=your-email-username
EMAIL_PASS=your-email-password
INNGEST_EVENT_KEY=your-inngest-event-key
INNGEST_SIGNING_KEY=your-inngest-signing-key
```

### Frontend Setup
```bash
cd ai-ticket-frontend
npm install
```

### Start Development Servers
```bash
# Backend
cd ai-ticket-assistant && npm run dev

# Inngest Dev Server
cd ai-ticket-assistant && npm run inngest-dev

# Frontend
cd ai-ticket-frontend && npm run dev
```

## 📖 Usage

### For Users
1. **Sign Up**: Create an account with email and password
2. **Create Tickets**: Submit support requests with title and description
3. **Track Progress**: Monitor ticket status and updates
4. **View Details**: Access detailed ticket information and AI-generated notes

### For Moderators
1. **Skill Registration**: Add your technical skills during signup
2. **Receive Assignments**: Get automatically assigned tickets matching your expertise
3. **Email Notifications**: Receive email alerts for new assignments
4. **Manage Tickets**: Update ticket status and add resolution notes

### For Admins
1. **System Overview**: Access all tickets and user management
2. **Fallback Assignment**: Handle tickets when no suitable moderator is found
3. **User Management**: Manage user roles and permissions

## 🔄 Workflow

1. **Ticket Creation**: User submits a new support ticket
2. **AI Analysis**: Gemini AI analyzes the ticket content and extracts:
   - Priority level (Low/Medium/High)
   - Required technical skills
   - Helpful resolution notes
3. **Smart Assignment**: System finds the best moderator based on skill matching
4. **Notification**: Assigned moderator receives email notification
5. **Resolution**: Moderator works on the ticket and updates status
6. **Completion**: Ticket is marked as resolved with final notes

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/signup` - User registration
- `POST /api/auth/login` - User login

### Tickets
- `GET /api/tickets` - Get all tickets (role-based filtering)
- `POST /api/tickets` - Create new ticket
- `GET /api/tickets/:id` - Get specific ticket details
- `PUT /api/tickets/:id` - Update ticket status/details

## 🤖 AI Integration

The system uses Google's Gemini AI model to:
- Analyze ticket descriptions for technical complexity
- Extract relevant skills and technologies mentioned
- Generate priority recommendations
- Provide helpful troubleshooting notes
- Suggest potential solutions or next steps

## 📧 Email Notifications

Automated email notifications are sent for:
- New ticket assignments to moderators
- Ticket status updates
- Priority escalations
- System alerts and updates

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Role-based access control
- CORS protection
- Input validation and sanitization

## 🚀 Deployment

### Production Environment Variables
Ensure all environment variables are properly configured for production:
- Database connection strings
- API keys and secrets
- Email service configuration
- Inngest production keys

### Build Commands
```bash
# Backend
cd ai-ticket-assistant
npm start

# Frontend
cd ai-ticket-frontend
npm run build
npm run preview
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation for troubleshooting guides
