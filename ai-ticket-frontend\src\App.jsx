import { BrowserRouter, Routes, Route } from "react-router-dom";
import Checkauth from "./compontents/check-auth.jsx";
import Tickets from "./pages/tickets.jsx";
import TicketDetailsPage from "./pages/ticket.jsx";
import Login from "./pages/login.jsx";
import Signup from "./pages/signup.jsx";
import Admin from "./pages/admin.jsx";

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route
          path="/"
          element={
            <Checkauth protected={true}>
              <Tickets />
            </Checkauth>
          }
        />
        <Route
          path="/tickets/:id"
          element={
            <Checkauth protected={true}>
              <TicketDetailsPage />
            </Checkauth>
          }
        />
        <Route
          path="/login"
          element={
            <Checkauth protected={false}>
              <Login />
            </Checkauth>
          }
        />
        <Route
          path="/signup"
          element={
            <Checkauth protected={false}>
              <Signup />
            </Checkauth>
          }
        />
        <Route
          path="/admin"
          element={
            <Checkauth protected={true}>
              <Admin />
            </Checkauth>
          }
        />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
