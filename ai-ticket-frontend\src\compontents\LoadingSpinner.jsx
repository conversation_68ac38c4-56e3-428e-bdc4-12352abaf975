export default function LoadingSpinner({ size = "md", text = "Loading..." }) {
  const sizeClasses = {
    sm: "loading-sm",
    md: "loading-md", 
    lg: "loading-lg"
  };

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <div className={`loading loading-spinner ${sizeClasses[size]} text-primary mb-4`}></div>
      <p className="text-slate-300 animate-pulse">{text}</p>
    </div>
  );
}

export function FullPageLoader({ text = "Loading..." }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="text-center">
        <div className="loading loading-spinner loading-lg text-primary mb-4"></div>
        <p className="text-white text-lg animate-pulse">{text}</p>
      </div>
    </div>
  );
}

export function CardLoader() {
  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 animate-pulse">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="h-4 bg-white/20 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-white/10 rounded w-1/2"></div>
        </div>
        <div className="h-6 w-16 bg-white/20 rounded-full"></div>
      </div>
      <div className="space-y-2">
        <div className="h-3 bg-white/10 rounded w-full"></div>
        <div className="h-3 bg-white/10 rounded w-2/3"></div>
      </div>
    </div>
  );
}
