import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const token = localStorage.getItem("token");
  let user = localStorage.getItem("user");
  if (user) {
    user = JSON.parse(user);
  }
  const navigate = useNavigate();

  const logout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    navigate("/login");
  };

  const getRoleColor = (role) => {
    switch (role) {
      case "admin": return "badge-error";
      case "moderator": return "badge-warning";
      default: return "badge-info";
    }
  };

  return (
    <div className="navbar bg-white/10 backdrop-blur-lg border-b border-white/20 sticky top-0 z-50">
      <div className="flex-1">
        <Link to="/" className="btn btn-ghost text-xl text-white hover:bg-white/20">
          <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent font-bold">
            🎫 AI Tickets
          </span>
        </Link>
      </div>

      <div className="flex-none">
        {!token ? (
          <div className="flex gap-2">
            <Link to="/signup" className="btn btn-sm btn-outline text-white border-white/30 hover:bg-white/20">
              Sign Up
            </Link>
            <Link to="/login" className="btn btn-sm btn-primary">
              Sign In
            </Link>
          </div>
        ) : (
          <div className="dropdown dropdown-end">
            <div
              tabIndex={0}
              role="button"
              className="btn btn-ghost btn-circle avatar placeholder"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full w-10 h-10 flex items-center justify-center">
                <span className="text-sm font-bold">
                  {user?.email?.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>

            {isMenuOpen && (
              <ul
                tabIndex={0}
                className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow-lg bg-white/10 backdrop-blur-lg rounded-box w-64 border border-white/20"
                onBlur={() => setIsMenuOpen(false)}
              >
                <li className="menu-title">
                  <span className="text-white">Account</span>
                </li>
                <li>
                  <div className="flex flex-col gap-1 p-2">
                    <span className="text-white font-medium">{user?.email}</span>
                    <span className={`badge ${getRoleColor(user?.role)} badge-sm`}>
                      {user?.role}
                    </span>
                    {user?.skills && user.skills.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {user.skills.slice(0, 3).map((skill, index) => (
                          <span key={index} className="badge badge-outline badge-xs text-white border-white/30">
                            {skill}
                          </span>
                        ))}
                        {user.skills.length > 3 && (
                          <span className="badge badge-outline badge-xs text-white border-white/30">
                            +{user.skills.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </li>

                <div className="divider my-1"></div>

                <li>
                  <Link to="/" className="text-white hover:bg-white/20">
                    🎫 My Tickets
                  </Link>
                </li>

                {user && (user.role === "admin" || user.role === "moderator") && (
                  <li>
                    <Link to="/admin" className="text-white hover:bg-white/20">
                      ⚙️ Admin Panel
                    </Link>
                  </li>
                )}

                <div className="divider my-1"></div>

                <li>
                  <button
                    onClick={logout}
                    className="text-red-300 hover:bg-red-500/20"
                  >
                    🚪 Sign Out
                  </button>
                </li>
              </ul>
            )}
          </div>
        )}
      </div>
    </div>
  );
}