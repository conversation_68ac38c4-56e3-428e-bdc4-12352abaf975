import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";

export default function SignupPage() {
  const [form, setForm] = useState({
    email: "",
    password: "",
    role: "user",
    skills: ""
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const navigate = useNavigate();

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
    // Clear error when user starts typing
    if (errors[e.target.name]) {
      setErrors({ ...errors, [e.target.name]: "" });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!form.email) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(form.email)) newErrors.email = "Email is invalid";

    if (!form.password) newErrors.password = "Password is required";
    else if (form.password.length < 6) newErrors.password = "Password must be at least 6 characters";

    if (form.role === "moderator" && !form.skills.trim()) {
      newErrors.skills = "Skills are required for moderators";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignup = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    try {
      const submitData = {
        ...form,
        skills: form.skills ? form.skills.split(",").map(skill => skill.trim()).filter(Boolean) : []
      };

      const res = await fetch(
        `${import.meta.env.VITE_SERVER_URL}/auth/signup`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(submitData),
        }
      );

      const data = await res.json();

      if (res.ok) {
        localStorage.setItem("token", data.token);
        localStorage.setItem("user", JSON.stringify(data.user));
        navigate("/");
      } else {
        setErrors({ general: data.error || "Signup failed" });
      }
    } catch (err) {
      setErrors({ general: "Something went wrong. Please try again." });
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const predefinedSkills = [
    "JavaScript", "React", "Node.js", "Python", "Java", "C++", "HTML/CSS",
    "MongoDB", "SQL", "AWS", "Docker", "Git", "TypeScript", "Vue.js", "Angular"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="card w-full max-w-md shadow-2xl bg-white/10 backdrop-blur-lg border border-white/20">
        <form onSubmit={handleSignup} className="card-body">
          <div className="text-center mb-6">
            <h2 className="text-3xl font-bold text-white mb-2">Join AI Tickets</h2>
            <p className="text-slate-300">Create your account to get started</p>
          </div>

          {errors.general && (
            <div className="alert alert-error mb-4">
              <span>{errors.general}</span>
            </div>
          )}

          <div className="form-control">
            <label className="label">
              <span className="label-text text-white">Email</span>
            </label>
            <input
              type="email"
              name="email"
              placeholder="Enter your email"
              className={`input input-bordered bg-white/20 border-white/30 text-white placeholder-white/70 ${
                errors.email ? "input-error" : ""
              }`}
              value={form.email}
              onChange={handleChange}
              required
            />
            {errors.email && <span className="text-error text-sm mt-1">{errors.email}</span>}
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text text-white">Password</span>
            </label>
            <input
              type="password"
              name="password"
              placeholder="Create a password"
              className={`input input-bordered bg-white/20 border-white/30 text-white placeholder-white/70 ${
                errors.password ? "input-error" : ""
              }`}
              value={form.password}
              onChange={handleChange}
              required
            />
            {errors.password && <span className="text-error text-sm mt-1">{errors.password}</span>}
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text text-white">Role</span>
            </label>
            <select
              name="role"
              className="select select-bordered bg-white/20 border-white/30 text-white"
              value={form.role}
              onChange={handleChange}
            >
              <option value="user">User - Submit tickets</option>
              <option value="moderator">Moderator - Handle tickets</option>
            </select>
          </div>

          {form.role === "moderator" && (
            <div className="form-control">
              <label className="label">
                <span className="label-text text-white">Skills</span>
              </label>
              <input
                type="text"
                name="skills"
                placeholder="e.g., JavaScript, React, Node.js"
                className={`input input-bordered bg-white/20 border-white/30 text-white placeholder-white/70 ${
                  errors.skills ? "input-error" : ""
                }`}
                value={form.skills}
                onChange={handleChange}
              />
              {errors.skills && <span className="text-error text-sm mt-1">{errors.skills}</span>}
              <div className="mt-2">
                <p className="text-xs text-slate-300 mb-2">Popular skills:</p>
                <div className="flex flex-wrap gap-1">
                  {predefinedSkills.slice(0, 8).map((skill) => (
                    <button
                      key={skill}
                      type="button"
                      className="badge badge-outline badge-sm text-white border-white/30 hover:bg-white/20"
                      onClick={() => {
                        const currentSkills = form.skills ? form.skills.split(",").map(s => s.trim()) : [];
                        if (!currentSkills.includes(skill)) {
                          setForm({ ...form, skills: [...currentSkills, skill].join(", ") });
                        }
                      }}
                    >
                      {skill}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="form-control mt-6">
            <button
              type="submit"
              className="btn btn-primary w-full"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  Creating Account...
                </>
              ) : (
                "Create Account"
              )}
            </button>
          </div>

          <div className="text-center mt-4">
            <p className="text-slate-300">
              Already have an account?{" "}
              <Link to="/login" className="text-primary hover:underline">
                Sign in
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}