import { useEffect, useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import ReactMarkdown from "react-markdown";
import Navbar from "../compontents/navbar";
import { FullPageLoader } from "../compontents/LoadingSpinner";

export default function TicketDetailsPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [newStatus, setNewStatus] = useState("");
  const [showStatusUpdate, setShowStatusUpdate] = useState(false);
  const [errors, setErrors] = useState({});

  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user") || "{}");

  const statusOptions = [
    { value: "TODO", label: "To Do", color: "badge-info" },
    { value: "IN_PROGRESS", label: "In Progress", color: "badge-warning" },
    { value: "COMPLETED", label: "Completed", color: "badge-success" }
  ];

  useEffect(() => {
    const fetchTicket = async () => {
      try {
        const res = await fetch(
          `${import.meta.env.VITE_SERVER_URL}/tickets/${id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const data = await res.json();
        if (res.ok) {
          setTicket(data.ticket);
          setNewStatus(data.ticket.status);
        } else {
          setErrors({ general: data.error || "Failed to fetch ticket" });
        }
      } catch (err) {
        console.error(err);
        setErrors({ general: "Something went wrong" });
      } finally {
        setLoading(false);
      }
    };

    fetchTicket();
  }, [id]);

  const handleStatusUpdate = async () => {
    if (newStatus === ticket.status) {
      setShowStatusUpdate(false);
      return;
    }

    setUpdating(true);
    setErrors({});

    try {
      const res = await fetch(
        `${import.meta.env.VITE_SERVER_URL}/tickets/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ status: newStatus }),
        }
      );

      const data = await res.json();
      if (res.ok) {
        setTicket({ ...ticket, status: newStatus });
        setShowStatusUpdate(false);
      } else {
        setErrors({ general: data.error || "Failed to update ticket" });
      }
    } catch (err) {
      console.error(err);
      setErrors({ general: "Something went wrong" });
    } finally {
      setUpdating(false);
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case "high": return "badge-error";
      case "medium": return "badge-warning";
      case "low": return "badge-success";
      default: return "badge-ghost";
    }
  };

  const getStatusColor = (status) => {
    const statusObj = statusOptions.find(s => s.value === status);
    return statusObj ? statusObj.color : "badge-ghost";
  };

  const canUpdateStatus = () => {
    return user.role === "admin" || user.role === "moderator" || ticket.assignedTo?._id === user._id;
  };

  if (loading) {
    return <FullPageLoader text="Loading ticket details..." />;
  }

  if (!ticket) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <Navbar />
        <div className="container mx-auto px-6 py-8">
          <div className="text-center py-12">
            <div className="text-6xl mb-4">❌</div>
            <h3 className="text-xl font-semibold text-white mb-2">Ticket not found</h3>
            <p className="text-slate-300 mb-6">The ticket you're looking for doesn't exist or you don't have permission to view it.</p>
            <Link to="/" className="btn btn-primary">
              ← Back to Tickets
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Navbar />

      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link to="/" className="btn btn-ghost text-white">
            ← Back to Tickets
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-white">Ticket Details</h1>
            <p className="text-slate-300">View and manage ticket information</p>
          </div>
        </div>

        {errors.general && (
          <div className="alert alert-error mb-6">
            <span>{errors.general}</span>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Ticket Header */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <div className="flex items-start justify-between mb-4">
                <h2 className="text-2xl font-bold text-white">{ticket.title}</h2>
                <div className="flex gap-2">
                  <span className={`badge ${getStatusColor(ticket.status)}`}>
                    {statusOptions.find(s => s.value === ticket.status)?.label || ticket.status}
                  </span>
                  {ticket.priority && (
                    <span className={`badge ${getPriorityColor(ticket.priority)}`}>
                      {ticket.priority}
                    </span>
                  )}
                </div>
              </div>

              <div className="prose prose-invert max-w-none">
                <p className="text-slate-300 text-lg leading-relaxed">
                  {ticket.description}
                </p>
              </div>
            </div>

            {/* AI Insights */}
            {ticket.helpfulNotes && (
              <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-lg rounded-xl p-6 border border-blue-500/20">
                <div className="flex items-center gap-2 mb-4">
                  <span className="text-2xl">🤖</span>
                  <h3 className="text-xl font-bold text-white">AI Analysis & Suggestions</h3>
                </div>
                <div className="prose prose-invert max-w-none">
                  <ReactMarkdown className="text-slate-300">
                    {ticket.helpfulNotes}
                  </ReactMarkdown>
                </div>
              </div>
            )}

            {/* Skills Required */}
            {ticket.relatedSkills?.length > 0 && (
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
                <h3 className="text-xl font-bold text-white mb-4">Required Skills</h3>
                <div className="flex flex-wrap gap-2">
                  {ticket.relatedSkills.map((skill, index) => (
                    <span key={index} className="badge badge-outline text-white border-white/30 px-3 py-2">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status Management */}
            {canUpdateStatus() && (
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4">Update Status</h3>

                {showStatusUpdate ? (
                  <div className="space-y-4">
                    <select
                      className="select select-bordered w-full bg-white/20 border-white/30 text-white"
                      value={newStatus}
                      onChange={(e) => setNewStatus(e.target.value)}
                    >
                      {statusOptions.map((status) => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>

                    <div className="flex gap-2">
                      <button
                        onClick={handleStatusUpdate}
                        className="btn btn-success btn-sm flex-1"
                        disabled={updating}
                      >
                        {updating ? (
                          <>
                            <span className="loading loading-spinner loading-sm"></span>
                            Updating...
                          </>
                        ) : (
                          "💾 Save"
                        )}
                      </button>
                      <button
                        onClick={() => {
                          setShowStatusUpdate(false);
                          setNewStatus(ticket.status);
                        }}
                        className="btn btn-ghost btn-sm text-white"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={() => setShowStatusUpdate(true)}
                    className="btn btn-primary btn-sm w-full"
                  >
                    ✏️ Update Status
                  </button>
                )}
              </div>
            )}

            {/* Ticket Info */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">Ticket Information</h3>

              <div className="space-y-3">
                <div>
                  <span className="text-slate-400 text-sm">Created</span>
                  <p className="text-white">
                    {new Date(ticket.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>

                <div>
                  <span className="text-slate-400 text-sm">Created by</span>
                  <p className="text-white">{ticket.createdBy?.email || "Unknown"}</p>
                </div>

                {ticket.assignedTo && (
                  <div>
                    <span className="text-slate-400 text-sm">Assigned to</span>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                        {ticket.assignedTo.email?.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <p className="text-white text-sm">{ticket.assignedTo.email}</p>
                        <span className="badge badge-outline badge-xs text-white border-white/30">
                          {ticket.assignedTo.role}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                {ticket.deadline && (
                  <div>
                    <span className="text-slate-400 text-sm">Deadline</span>
                    <p className="text-white">
                      {new Date(ticket.deadline).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">Quick Actions</h3>

              <div className="space-y-2">
                <Link to="/" className="btn btn-ghost btn-sm w-full text-white justify-start">
                  📋 View All Tickets
                </Link>

                {user.role === "admin" && (
                  <Link to="/admin" className="btn btn-ghost btn-sm w-full text-white justify-start">
                    ⚙️ Admin Panel
                  </Link>
                )}

                <button
                  onClick={() => window.print()}
                  className="btn btn-ghost btn-sm w-full text-white justify-start"
                >
                  🖨️ Print Ticket
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}