import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import Navbar from "../compontents/navbar";
import LoadingSpinner, { CardLoader } from "../compontents/LoadingSpinner";

export default function Tickets() {
  const [form, setForm] = useState({ title: "", description: "" });
  const [tickets, setTickets] = useState([]);
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [errors, setErrors] = useState({});

  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user") || "{}");

  const fetchTickets = async () => {
    try {
      setLoading(true);
      const res = await fetch(`${import.meta.env.VITE_SERVER_URL}/tickets/getTickets`, {
        headers: { Authorization: `Bearer ${token}` },
        method: "GET",
      });
      const data = await res.json();
      setTickets(data || []);
      setFilteredTickets(data || []);
    } catch (err) {
      console.error("Failed to fetch tickets:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
  }, []);

  useEffect(() => {
    let filtered = tickets;

    // Filter by status
    if (filter !== "all") {
      filtered = filtered.filter(ticket => ticket.status === filter);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(ticket =>
        ticket.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredTickets(filtered);
  }, [tickets, filter, searchQuery]);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
    // Clear errors when user starts typing
    if (errors[e.target.name]) {
      setErrors({ ...errors, [e.target.name]: "" });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!form.title.trim()) newErrors.title = "Title is required";
    if (!form.description.trim()) newErrors.description = "Description is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setCreating(true);
    setErrors({});

    try {
      const res = await fetch(`${import.meta.env.VITE_SERVER_URL}/tickets`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(form),
      });

      const data = await res.json();

      if (res.ok) {
        setForm({ title: "", description: "" });
        setShowForm(false);
        fetchTickets(); // Refresh list
      } else {
        setErrors({ general: data.error || "Ticket creation failed" });
      }
    } catch (err) {
      setErrors({ general: "Error creating ticket. Please try again." });
      console.error(err);
    } finally {
      setCreating(false);
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case "high": return "badge-error";
      case "medium": return "badge-warning";
      case "low": return "badge-success";
      default: return "badge-ghost";
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "COMPLETED": return "badge-success";
      case "IN_PROGRESS": return "badge-warning";
      case "TODO": return "badge-info";
      default: return "badge-ghost";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <Navbar />
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <CardLoader key={i} />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Navbar />

      <div className="container mx-auto px-6 py-8">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">
              Support Tickets
            </h1>
            <p className="text-slate-300">
              Manage your support requests with AI-powered assistance
            </p>
          </div>

          <button
            onClick={() => setShowForm(!showForm)}
            className="btn btn-primary mt-4 lg:mt-0"
          >
            {showForm ? "Cancel" : "🎫 Create New Ticket"}
          </button>
        </div>

        {/* Create Ticket Form */}
        {showForm && (
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-8 border border-white/20">
            <h2 className="text-2xl font-bold text-white mb-4">Create New Ticket</h2>

            {errors.general && (
              <div className="alert alert-error mb-4">
                <span>{errors.general}</span>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text text-white">Title</span>
                </label>
                <input
                  name="title"
                  value={form.title}
                  onChange={handleChange}
                  placeholder="Brief description of your issue"
                  className={`input input-bordered bg-white/20 border-white/30 text-white placeholder-white/70 ${
                    errors.title ? "input-error" : ""
                  }`}
                  required
                />
                {errors.title && <span className="text-error text-sm mt-1">{errors.title}</span>}
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text text-white">Description</span>
                </label>
                <textarea
                  name="description"
                  value={form.description}
                  onChange={handleChange}
                  placeholder="Detailed description of your issue, including steps to reproduce, expected behavior, etc."
                  className={`textarea textarea-bordered h-32 bg-white/20 border-white/30 text-white placeholder-white/70 ${
                    errors.description ? "textarea-error" : ""
                  }`}
                  required
                />
                {errors.description && <span className="text-error text-sm mt-1">{errors.description}</span>}
              </div>

              <div className="flex gap-3">
                <button
                  type="submit"
                  className="btn btn-primary flex-1"
                  disabled={creating}
                >
                  {creating ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Creating Ticket...
                    </>
                  ) : (
                    "🚀 Create Ticket"
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="btn btn-ghost text-white"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-8 border border-white/20">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="🔍 Search tickets..."
                className="input input-bordered w-full bg-white/20 border-white/30 text-white placeholder-white/70"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <select
                className="select select-bordered bg-white/20 border-white/30 text-white"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="TODO">To Do</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="COMPLETED">Completed</option>
              </select>
            </div>
          </div>

          <div className="flex items-center justify-between mt-4">
            <span className="text-slate-300">
              {filteredTickets.length} of {tickets.length} tickets
            </span>
            <div className="flex gap-2">
              <span className="badge badge-info badge-sm">TODO: {tickets.filter(t => t.status === "TODO").length}</span>
              <span className="badge badge-warning badge-sm">IN PROGRESS: {tickets.filter(t => t.status === "IN_PROGRESS").length}</span>
              <span className="badge badge-success badge-sm">COMPLETED: {tickets.filter(t => t.status === "COMPLETED").length}</span>
            </div>
          </div>
        </div>

        {/* Tickets Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredTickets.map((ticket) => (
            <Link
              key={ticket._id}
              to={`/tickets/${ticket._id}`}
              className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              <div className="flex items-start justify-between mb-4">
                <h3 className="font-bold text-lg text-white line-clamp-2">
                  {ticket.title}
                </h3>
                <div className="flex flex-col gap-1">
                  <span className={`badge ${getStatusColor(ticket.status)} badge-sm`}>
                    {ticket.status}
                  </span>
                  {ticket.priority && (
                    <span className={`badge ${getPriorityColor(ticket.priority)} badge-sm`}>
                      {ticket.priority}
                    </span>
                  )}
                </div>
              </div>

              <p className="text-slate-300 text-sm mb-4 line-clamp-3">
                {ticket.description}
              </p>

              <div className="flex items-center justify-between text-xs text-slate-400">
                <span>
                  Created {new Date(ticket.createdAt).toLocaleDateString()}
                </span>
                {ticket.assignedTo && (
                  <span className="badge badge-outline badge-xs text-white border-white/30">
                    Assigned
                  </span>
                )}
              </div>

              {ticket.relatedSkills && ticket.relatedSkills.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-3">
                  {ticket.relatedSkills.slice(0, 3).map((skill, index) => (
                    <span key={index} className="badge badge-outline badge-xs text-white border-white/30">
                      {skill}
                    </span>
                  ))}
                  {ticket.relatedSkills.length > 3 && (
                    <span className="badge badge-outline badge-xs text-white border-white/30">
                      +{ticket.relatedSkills.length - 3}
                    </span>
                  )}
                </div>
              )}
            </Link>
          ))}
        </div>

        {/* Empty State */}
        {filteredTickets.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎫</div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {tickets.length === 0 ? "No tickets yet" : "No tickets match your filters"}
            </h3>
            <p className="text-slate-300 mb-6">
              {tickets.length === 0
                ? "Create your first support ticket to get started"
                : "Try adjusting your search or filter criteria"
              }
            </p>
            {tickets.length === 0 && (
              <button
                onClick={() => setShowForm(true)}
                className="btn btn-primary"
              >
                🚀 Create Your First Ticket
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}